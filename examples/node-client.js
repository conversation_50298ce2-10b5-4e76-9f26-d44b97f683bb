const { io } = require('socket.io-client');
const Y = require('yjs');

/**
 * Node.js YJS Client Example
 * Demonstrates how to connect to the YJS server from a Node.js application
 */
class NodeYjsClient {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.socket = null;
    this.doc = new Y.Doc();
    this.text = this.doc.getText('content');
    this.connected = false;
    this.documentId = null;
    this.userId = null;
    
    this.setupDocumentListeners();
  }

  /**
   * Setup YJS document event listeners
   */
  setupDocumentListeners() {
    this.doc.on('update', (update, origin) => {
      if (origin !== this.socket?.id && this.connected) {
        this.socket.emit('yjs-update', {
          documentId: this.documentId,
          update: Array.from(update)
        });
        console.log('📤 Sent update to server');
      }
    });

    this.text.observe((event) => {
      console.log('📝 Document content changed:', this.text.toString());
    });
  }

  /**
   * Connect to the YJS server
   */
  async connect(documentId, userId) {
    return new Promise((resolve, reject) => {
      this.documentId = documentId;
      this.userId = userId;

      console.log(`🔌 Connecting to ${this.serverUrl}...`);
      
      this.socket = io(this.serverUrl);

      this.socket.on('connect', () => {
        console.log('✅ Connected to server');
        this.connected = true;
        
        this.socket.emit('join-document', {
          documentId: this.documentId,
          userId: this.userId
        });
      });

      this.socket.on('joined-document', (data) => {
        console.log(`🏠 Joined document: ${data.documentId} (${data.connectedUsers} users)`);
        resolve(data);
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Disconnected from server');
        this.connected = false;
      });

      this.socket.on('yjs-update', (data) => {
        if (data.origin !== this.socket.id) {
          const update = new Uint8Array(data.update);
          Y.applyUpdate(this.doc, update, this.socket.id);
          console.log('📥 Received update from server');
        }
      });

      this.socket.on('sync-response', (data) => {
        const state = new Uint8Array(data.state);
        Y.applyUpdate(this.doc, state, this.socket.id);
        console.log(`🔄 Synced document (${data.type})`);
      });

      this.socket.on('user-joined', (data) => {
        console.log(`👋 User joined: ${data.userId}`);
      });

      this.socket.on('user-left', (data) => {
        console.log(`👋 User left: ${data.userId}`);
      });

      this.socket.on('error', (error) => {
        console.error('❌ Socket error:', error);
        reject(error);
      });

      this.socket.on('server-shutdown', (data) => {
        console.log('🛑 Server shutdown:', data.message);
      });

      // Connection timeout
      setTimeout(() => {
        if (!this.connected) {
          reject(new Error('Connection timeout'));
        }
      }, 10000);
    });
  }

  /**
   * Insert text at the beginning of the document
   */
  insertText(text) {
    this.text.insert(0, text);
    console.log(`✏️  Inserted text: "${text}"`);
  }

  /**
   * Append text to the document
   */
  appendText(text) {
    this.text.insert(this.text.length, text);
    console.log(`✏️  Appended text: "${text}"`);
  }

  /**
   * Replace all text in the document
   */
  replaceText(text) {
    this.text.delete(0, this.text.length);
    this.text.insert(0, text);
    console.log(`✏️  Replaced text with: "${text}"`);
  }

  /**
   * Get current document content
   */
  getContent() {
    return this.text.toString();
  }

  /**
   * Disconnect from the server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      console.log('🔌 Disconnected from server');
    }
  }

  /**
   * Get server statistics
   */
  async getServerStats() {
    try {
      const response = await fetch(`${this.serverUrl}/api/stats`);
      const stats = await response.json();
      return stats;
    } catch (error) {
      console.error('Failed to fetch server stats:', error);
      throw error;
    }
  }
}

/**
 * Example usage
 */
async function example() {
  const client = new NodeYjsClient();
  
  try {
    // Connect to document
    await client.connect('example-document', 'node-client-' + Date.now());
    
    // Wait a bit for initial sync
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('📄 Current content:', client.getContent());
    
    // Make some changes
    client.appendText('\nHello from Node.js client! ');
    client.appendText(`Time: ${new Date().toISOString()}`);
    
    // Wait for changes to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('📄 Updated content:', client.getContent());
    
    // Get server stats
    try {
      const stats = await client.getServerStats();
      console.log('📊 Server stats:', JSON.stringify(stats, null, 2));
    } catch (error) {
      console.log('⚠️  Could not fetch server stats (server might not be running)');
    }
    
    // Keep connection alive for a while
    console.log('⏰ Keeping connection alive for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    client.disconnect();
    process.exit(0);
  }
}

// Run example if this file is executed directly
if (require.main === module) {
  console.log('🚀 Starting Node.js YJS Client Example');
  console.log('Make sure the YJS server is running on http://localhost:3000');
  console.log('You can also open examples/client.html in a browser to see real-time collaboration');
  console.log('');
  
  example().catch(console.error);
}

module.exports = NodeYjsClient;
